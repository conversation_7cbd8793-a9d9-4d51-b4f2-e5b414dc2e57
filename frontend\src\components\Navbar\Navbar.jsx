import { useState, useEffect, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import logo_white from '../../assets/photos/logo_white.png';
import logo_black from '../../assets/photos/logo_black1.png';
import { useAuthStore } from '../../store/useAuthStore';
import UserMenu from '../ui/UserMenu';
import AuthModals from '../Login/AuthModals';




const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const [signupModalOpen, setSignupModalOpen] = useState(false);
  const location = useLocation();
  const { user } = useAuthStore();

  // Handle scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [location]);

  const handleLoginClick = useCallback(() => {
    setLoginModalOpen(true);
  }, []);

  const handleSignupClick = useCallback(() => {
    setSignupModalOpen(true);
  }, []);

  const handleModalClose = useCallback(() => {
    setLoginModalOpen(false);
    setSignupModalOpen(false);
  }, []);

  return (
    <>
      <nav
        className={`fixed top-0 left-0 w-full z-[999] transition-all duration-300 ${
          scrolled ? "bg-white shadow-md py-3" : "bg-slate-800 py-4"
        }`}
      >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center">
          {/* Simple Logo */}
          <Link to="/" className="flex items-center">
            <div
              className={`flex items-center ${
                scrolled ? "text-slate-800" : "text-white"
              }`}
            >
              <img
                src={scrolled ? logo_black : logo_white}
                alt="TrypIndia Logo"
                className="h-11 w-auto object-contain mr-3 brightness-125 contrast-150"
              />
              <span className="text-2xl font-bold">
                TRYP<span className='text-blue-500'>INDIA</span>
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            {[
              { name: "Home", path: "/" },
              { name: "Packages", path: "/packages" },
              { name: "Blog", path: "/blog" },
              { name: "About", path: "/about" },
              { name: "Contact", path: "/contact" },
            ].map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`text-sm font-medium transition-colors duration-200 ${
                  location.pathname === item.path
                    ? scrolled
                      ? "text-blue-600 border-b-2 border-indigo-600 pb-1"
                      : "text-indigo-300 border-b-2 border-indigo-300 pb-1"
                    : scrolled
                    ? "text-slate-600 hover:text-blue-500"
                    : "text-gray-300 hover:text-white"
                }`}
              >
                {item.name}
              </Link>
            ))}
          </div>



          <div className="flex items-center space-x-4">
            {user ? (
              <UserMenu />
            ) : (
              <button
                onClick={handleLoginClick}
                className="px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 bg-blue-400 hover:bg-blue-500 text-white"
              >
                Login
              </button>
            )}
          </div>


         

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className={`p-2 rounded-md ${
                scrolled
                  ? "text-gray-600 hover:text-slate-800"
                  : "text-gray-300 hover:text-white"
              } focus:outline-none`}
            >
              {isOpen ? (
                <i className="fas fa-times text-xl"></i>
              ) : (
                <i className="fas fa-bars text-xl"></i>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isOpen && (
        <div
          className={`md:hidden ${
            scrolled ? "bg-white" : "bg-slate-800"
          } border-t ${scrolled ? "border-gray-200" : "border-slate-700"}`}
        >
          <div className="px-4 py-4 space-y-3">
            {[
              { name: "Home", path: "/" },
              { name: "Plan Your Trip", path: "/packages" },
              { name: "About", path: "/about" },
              { name: "Gallery", path: "/gallery" },
              { name: "Blog", path: "/blog" },
              { name: "Contact", path: "/contact" },
            ].map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`block px-3 py-2 rounded-md text-base font-medium ${
                  location.pathname === item.path
                    ? scrolled
                      ? "bg-indigo-100 text-indigo-700"
                      : "bg-indigo-700 text-white"
                    : scrolled
                    ? "text-slate-600 hover:bg-gray-100"
                    : "text-gray-300 hover:bg-slate-700 hover:text-white"
                }`}
              >
                {item.name}
              </Link>
            ))}

            {!user && (
              <div className="pt-3">
                <button
                  onClick={handleLoginClick}
                  className={`block w-full text-center px-4 py-2 rounded-md text-base font-medium ${
                    scrolled
                      ? "bg-indigo-600 text-white hover:bg-indigo-700"
                      : "bg-indigo-600 text-white hover:bg-indigo-500"
                  }`}
                >
                  Login / Sign Up
                </button>
              </div>
            )}
          </div>
        </div>
      )}
      </nav>

      {/* Auth Modals */}
      <AuthModals
        loginOpen={loginModalOpen}
        signupOpen={signupModalOpen}
        onClose={handleModalClose}
      />
    </>
  );
};

export default Navbar;
