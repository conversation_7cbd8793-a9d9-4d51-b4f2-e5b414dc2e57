import React, { useState, useEffect, lazy } from 'react'
import './App.css'
import '@fortawesome/fontawesome-free/css/all.min.css'
import { Routes, Route, useLocation } from 'react-router-dom';
import { preloadCriticalImages } from './utils/imageOptimization';
import SEO from './components/common/SEO';
import { useAuthStore } from './store/useAuthStore';
import { Toaster } from 'react-hot-toast';
import MainLayout from './layout/MainLayout';
import Spinner from './components/ui/Spinner';


// Lazy load components
const Home = lazy(() => import('./pages/Home'));
const Plan = lazy(() => import('./pages/Plan'));
const About = lazy(() => import('./pages/About'));
const Gallery = lazy(() => import('./pages/Gallery'));
const Contact = lazy(() => import('./pages/contact'));
const Blog = lazy(() => import('./pages/Blog'));
const BlogDetails = lazy(() => import('./components/Blog/Blog_Details'));
const ChatBot = lazy(() => import('./components/ChatBot/ChatBot'));
const PackageDetails = lazy(() => import('./components/Package/Package_Details'));
const Wishlist = lazy(() => import('./components/ui/Wishlist'));
const Profile = lazy(() => import('./components/ui/Profile'));





// Import AOS with reduced features
import AOS from 'aos';
import 'aos/dist/aos.css';

const App = () => {
  const location = useLocation();
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const checkAuth = useAuthStore((state) => state.checkAuth);

  useEffect(() => {
    // Initialize AOS with optimized settings
    AOS.init({
      duration: 800,
      once: true, // Only animate elements once
      mirror: false, // Don't mirror animations when scrolling up
      offset: 100,
      disable: 'mobile', // Disable on mobile for better performance
    });

    // Preload critical images for better LCP
    preloadCriticalImages([
      // Add paths to critical images here
      '/src/assets/photos/city_gangtok.webp',
      '/src/assets/photos/dargeeling2.webp'
    ]);

    // Check authentication on app load
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      // Remove console.log to reduce main thread work
    };

    // Call it once on mount
    handleResize();

    // Debounce resize event for better performance
    let resizeTimer;
    const debouncedResize = () => {
      clearTimeout(resizeTimer);
      resizeTimer = setTimeout(handleResize, 100);
    };

    window.addEventListener('resize', debouncedResize);
    return () => {
      window.removeEventListener('resize', debouncedResize);
      clearTimeout(resizeTimer);
    };
  }, []);

  const isHomePage = location.pathname === '/';
  const isGalleryPage = location.pathname === '/gallery';

  return (
    <div className="flex flex-col min-h-screen bg-white text-black">
      {/* Default SEO - will be overridden by page-specific SEO components */}
      <SEO
        title="Trypindia - Gateway to North East India"
        description="Discover the enchanting beauty of North East India with our personalized travel packages. Plan your dream vacation to Darjeeling, Gangtok, and Sikkim."
        keywords="travel, north east india, darjeeling, gangtok, sikkim, travel packages, tourism"
      />



      

      {/* Main Content */}
      <main
        className={`main-content w-full ${
          isHomePage && isMobile ? "home-page-mobile" : ""
        } ${isGalleryPage ? "gallery-page" : ""}`}
      >
        <React.Suspense
          fallback={
            <Spinner />
          }
        >
          <Routes>
            <Route path="/" element={<MainLayout><Home /></MainLayout>} />
            <Route path="/plan" element={<MainLayout><Plan /></MainLayout>} />
            <Route path="/packages" element={<MainLayout><Plan /></MainLayout>} />
            <Route path="/package/:id" element={<MainLayout><PackageDetails /></MainLayout>} />
            <Route path="/about" element={<MainLayout><About /></MainLayout>} />
            <Route path="/gallery" element={<MainLayout><Gallery /></MainLayout>} />
            <Route path="/contact" element={<MainLayout><Contact /></MainLayout>} />
            <Route path="/blog" element={<MainLayout><Blog /></MainLayout>} />
            <Route path="/blog/:id" element={<MainLayout><BlogDetails /></MainLayout>} />
            <Route path="/wishlist" element={<MainLayout><Wishlist /></MainLayout>} />
            <Route path="/profile" element={<MainLayout><Profile /></MainLayout>} />

          </Routes>
        </React.Suspense>
      </main>

      {/* ChatBot Component - Lazy loaded */}
      <React.Suspense fallback={<div></div>}>
        <ChatBot />
      </React.Suspense>


      <Toaster />

    </div>
  );
};

export default App;