import toast from 'react-hot-toast';
import {create} from 'zustand';
import { axiosInstance } from '../lib/axios';

const BASE_URL =
  import.meta.env.MODE === "development" ? "http://localhost:3000" : "/";

export const useAuthStore = create((set) => ({
    user: null,
    isLoading: false,

    checkAuth: async () => {
        try {
            set({ isLoading: true });
            const response = await axiosInstance.get('/auth/check');
            const { user } = response.data;
            set({ user });
        } catch (error) {
            console.log(error);
            set({ user: null });
        }finally {
            set({ isLoading: false });
        }
    },

    signup: async (data) => {
        const { name, email, password } = data;
        try {
            set({ isLoading: true });
            const response = await axiosInstance.post('/auth/signup', { name, email, password });
            toast.success(response.data.message);
            const { user } = response.data;
            set({ user });
        } catch (error) {
            console.log(error);
            toast.error(error.response?.data?.message || 'Signup failed');
        } finally {
            set({ isLoading: false });
        }
    },

    login: async (email, password) => {
        try {
            set({ isLoading: true });
            const response = await axiosInstance.post('/auth/login', { email, password });
            toast.success(response.data.message);
            const { user } = response.data;
            set({ user });
        } catch (error) {
            toast.error(error.response?.data?.message || 'Login failed');
        }finally {
            set({ isLoading: false });
        }
    },

    logout: async () => {
        try {
            set({ isLoading: true });
            const response = await axiosInstance.post('/auth/logout');
            toast.success(response.data.message);
            set({ user: null });
        } catch (error) {
            toast.error(error.response?.data?.message || 'Logout failed');
        } finally {
            set({ isLoading: false });
        }
    },

}));

