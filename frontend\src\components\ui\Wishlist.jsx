import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../store/useAuthStore';
import { useWishlistStore } from '../../store/useWishlistStore';
import SEO from '../common/SEO';
import darjeeling from '../../assets/photos/dargeeling2.webp';
import gangtok from '../../assets/photos/dooars.webp';
import northSikkim from '../../assets/photos/plan2.png';

// Sample package data - in a real app, this would come from your API
const samplePackages = [
  {
    id: 1,
    location: "Darjeeling",
    duration: "3D/2N",
    no_of_persons: "2",
    price: "₹15,999",
    image: darjeeling,
    description: "Experience the charm of the Queen of Hills with tea gardens, toy train rides, and stunning mountain views.",
    included: [
      { icon: "fa-car", text: "Private Car" },
      { icon: "fa-bed", text: "Hotel Stay" },
      { icon: "fa-camera", text: "Sightseeing" },
      { icon: "fa-headset", text: "24/7 Support" }
    ]
  },
  {
    id: 2,
    location: "Darjeeling-Gangtok",
    duration: "4D/3N",
    no_of_persons: "2",
    price: "₹18,999",
    image: gangtok,
    description: "Explore the best of both worlds - Darjeeling's tea culture and Gangtok's Buddhist heritage.",
    included: [
      { icon: "fa-car", text: "Private Car" },
      { icon: "fa-bed", text: "Hotel Stay" },
      { icon: "fa-camera", text: "Sightseeing" },
      { icon: "fa-headset", text: "24/7 Support" }
    ]
  },
  {
    id: 3,
    location: "Gangtok-North Sikkim",
    duration: "5D/4N",
    no_of_persons: "2",
    price: "₹22,999",
    image: northSikkim,
    description: "Adventure through high-altitude lakes, monasteries, and breathtaking Himalayan landscapes.",
    included: [
      { icon: "fa-car", text: "Private Car" },
      { icon: "fa-bed", text: "Hotel Stay" },
      { icon: "fa-camera", text: "Sightseeing" },
      { icon: "fa-headset", text: "24/7 Support" }
    ]
  }
];

const Wishlist = () => {
  const { user } = useAuthStore();
  const navigate = useNavigate();
  const { wishlistItems: wishlistIds, removeFromWishlist: removeFromWishlistStore, initializeWishlist } = useWishlistStore();
  const [wishlistItems, setWishlistItems] = useState([]);
  const [loading, setLoading] = useState(true);

  // Initialize wishlist when user changes
  useEffect(() => {
    if (!user) {
      navigate('/');
      return;
    }

    // Initialize wishlist store
    initializeWishlist(user._id);
    setLoading(false);
  }, [user, navigate, initializeWishlist]);

  // Update wishlist items when wishlistIds change
  useEffect(() => {
    if (user) {
      // Filter sample packages to get only wishlisted items
      const wishlistPackages = samplePackages.filter(pkg =>
        wishlistIds.includes(pkg.id)
      );
      setWishlistItems(wishlistPackages);
    }
  }, [wishlistIds, user]);

  // Remove item from wishlist
  const handleRemoveFromWishlist = (packageId) => {
    removeFromWishlistStore(packageId, user._id);
    // Update local state to reflect the change immediately
    const updatedItems = wishlistItems.filter(item => item.id !== packageId);
    setWishlistItems(updatedItems);
  };

  // Navigate to package details
  const handleViewDetails = (packageId) => {
    window.scrollTo(0, 0);
    navigate(`/package/${packageId}`);
  };

  // Navigate to booking
  const handleBookNow = (packageId) => {
    navigate('/contact', { state: { selectedPackage: packageId } });
  };

  if (!user) {
    return null;
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 pt-24 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your wishlist...</p>
        </div>
      </div>
    );
  }

  return (
    <>
      <SEO
        title="My Wishlist - TrypIndia"
        description="View and manage your saved travel packages"
      />

      <div className="min-h-screen bg-gray-50 pt-24 pb-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4 font-serif">
              My Wishlist
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Your saved travel packages for future adventures
            </p>
          </div>

          {/* Wishlist Content */}
          {wishlistItems.length === 0 ? (
            // Empty State
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className="fas fa-heart text-4xl text-gray-400"></i>
              </div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                Your wishlist is empty
              </h3>
              <p className="text-gray-600 mb-8 max-w-md mx-auto">
                Start exploring our amazing travel packages and save your favorites for later!
              </p>
              <button
                onClick={() => navigate('/plan')}
                className="bg-blue-400 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-500 transition-colors inline-flex items-center"
              >
                <i className="fas fa-search mr-2"></i>
                Explore Packages
              </button>
            </div>
          ) : (
            // Wishlist Items
            <div>
              {/* Stats */}
              <div className="mb-8 bg-white rounded-xl shadow-sm p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900">
                      Saved Packages
                    </h2>
                    <p className="text-gray-600">
                      {wishlistItems.length} package{wishlistItems.length !== 1 ? 's' : ''} in your wishlist
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">Total estimated value</p>
                    <p className="text-2xl font-bold text-blue-600">
                      ₹{wishlistItems.reduce((total, item) =>
                        total + parseInt(item.price.replace(/[₹,]/g, '')), 0
                      ).toLocaleString()}
                    </p>
                  </div>
                </div>
              </div>

              {/* Package Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {wishlistItems.map(pkg => (
                  <div
                    key={pkg.id}
                    className="group bg-white rounded-2xl overflow-hidden shadow-lg transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl"
                  >
                    {/* Package Image */}
                    <div className="relative h-64">
                      <img
                        src={pkg.image}
                        alt={pkg.location}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

                      {/* Remove from Wishlist Button */}
                      <button
                        onClick={() => handleRemoveFromWishlist(pkg.id)}
                        className="absolute top-4 right-4 w-10 h-10 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center text-red-500 hover:bg-white hover:text-red-600 transition-all duration-200 shadow-lg"
                        title="Remove from wishlist"
                      >
                        <i className="fas fa-heart"></i>
                      </button>

                      {/* Package Info Overlay */}
                      <div className="absolute bottom-4 left-4 text-white">
                        <h3 className="text-xl font-bold mb-1">{pkg.location}</h3>
                        <div className="flex items-center space-x-4 text-sm">
                          <span className="flex items-center">
                            <i className="fas fa-clock mr-1"></i>
                            {pkg.duration}
                          </span>
                          <span className="flex items-center">
                            <i className="fas fa-users mr-1"></i>
                            {pkg.no_of_persons} persons
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Package Details */}
                    <div className="p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div className="text-2xl font-bold text-blue-600">
                          {pkg.price}
                        </div>
                        <div className="text-sm text-gray-500">per person</div>
                      </div>

                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {pkg.description}
                      </p>

                      {/* Included Features */}
                      <div className="mb-6">
                        <h4 className="text-sm font-semibold text-gray-900 mb-3">Included:</h4>
                        <div className="grid grid-cols-2 gap-2">
                          {pkg.included.slice(0, 4).map((item, index) => (
                            <div key={index} className="flex items-center text-sm text-gray-600">
                              <div className="w-6 h-6 bg-gray-100 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                                <i className={`fas ${item.icon} text-xs text-gray-700`}></i>
                              </div>
                              <span className="truncate">{item.text}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex space-x-3">
                        <button
                          onClick={() => handleViewDetails(pkg.id)}
                          className="flex-1 py-2.5 border border-blue-400 text-blue-400 font-medium rounded-lg hover:bg-blue-50 transition-colors text-sm"
                        >
                          View Details
                        </button>
                        <button
                          onClick={() => handleBookNow(pkg.id)}
                          className="flex-1 py-2.5 bg-blue-400 text-white font-medium rounded-lg hover:bg-blue-500 transition-colors text-sm"
                        >
                          Book Now
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Continue Exploring */}
              <div className="mt-12 text-center">
                <div className="bg-white rounded-xl shadow-sm p-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-4">
                    Looking for more adventures?
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Discover more amazing travel packages and add them to your wishlist
                  </p>
                  <button
                    onClick={() => navigate('/plan')}
                    className="bg-blue-400 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-500 transition-colors inline-flex items-center"
                  >
                    <i className="fas fa-plus mr-2"></i>
                    Explore More Packages
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default Wishlist;