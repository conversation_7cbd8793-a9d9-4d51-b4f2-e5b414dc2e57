import { useState, useEffect, useCallback } from 'react';
import LoginModal from './LoginModal';
import SignupModal from './SignupModal';

const AuthModals = ({ loginOpen, signupOpen, onClose }) => {
  const [currentModal, setCurrentModal] = useState(null);

  // Update currentModal when props change
  useEffect(() => {
    if (loginOpen) {
      setCurrentModal('login');
    } else if (signupOpen) {
      setCurrentModal('signup');
    } else {
      setCurrentModal(null);
    }
  }, [loginOpen, signupOpen]);

  const handleClose = useCallback(() => {
    setCurrentModal(null);
    onClose();
  }, [onClose]);

  const switchToSignup = useCallback(() => {
    setCurrentModal('signup');
  }, []);

  const switchToLogin = useCallback(() => {
    setCurrentModal('login');
  }, []);

  return (
    <>
      <LoginModal
        open={currentModal === 'login'}
        onClose={handleClose}
        onSwitchToSignup={switchToSignup}
      />
      <SignupModal
        open={currentModal === 'signup'}
        onClose={handleClose}
        onSwitchToLogin={switchToLogin}
      />
    </>
  );
};

export default AuthModals;
